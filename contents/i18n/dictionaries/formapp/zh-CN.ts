const dict = {
  toolsdk: {
    metadata: {
      title: 'ToolSDK.ai: 5000+ MCP Servers & AI Tools, 1 Line of Code ',
      description:
        'Free TypeScript SDK for building agentic AI apps — with instant access to MCP servers.Use Cases: Connect your AI agents and automation workflow apps with over 5000 MCP servers and AI tools.',
      keywords:
        'ToolSDK.ai, TypeScript SDK, MCP servers, AI agent integration, OpenAI SDK, Vercel AI SDK, AI automation workflows, MCP server registry, GPT-4.1 tools, AI agent development, MCP server connection, AI tool orchestration',
    },
    website: {
      title: 'AI Tools, 1 Line of Code',
      description: 'A free TypeScript SDK for building agentic AI apps — with instant access to MCP servers.',
    },
    component: {
      codeSection: {
        title: 'Quick Starting',
        description: 'More details and complete examples on',
        examples: {
          AISDK: {
            comment: {
              initialize: 'Initialize ToolSDK',
              useAISDK: 'Use AI SDK to call tools',
            },
          },
          OPENAI: {
            comment: {
              initialize: 'Initialize OpenAI and ToolSDK',
              callChatGPT: 'Call ChatGPT and handle tool calls',
              handleToolCalls: 'Handle tool calls',
              executeTool: 'Execute tool using ToolSDK',
            },
          },
        },
      },
    },
  },
};

export default dict;
