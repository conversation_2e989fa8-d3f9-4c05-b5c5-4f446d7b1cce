import { iStringParse, iString } from '@bika/types/i18n/bo';
import { Locale } from './config';
import type { ILocaleContext } from './context';
import en from './dictionaries/en';
import toolsdkEN from './dictionaries/formapp/en';
import toolSDKJA from './dictionaries/formapp/ja';
import toolSDKzhCN from './dictionaries/formapp/zh-CN';
import toolSDKzhTW from './dictionaries/formapp/zh-TW';
import ja from './dictionaries/ja';
import zhCN from './dictionaries/zh-CN';
import zhTW from './dictionaries/zh-TW';
import { getTranslations, type Dictionary, type FormAppDictionary } from './translate';

// eslint-disable-next-line @typescript-eslint/no-explicit-any

// 客户端禁止使用，这是sync的方式获取Dictionary； 尽可能另外使用 translate.ts 里的 getDictionary， async的方式
export const getServerDictionary = (locale: Locale = 'en'): Dictionary => {
  switch (locale) {
    case 'en':
      return en as Dictionary;
    case 'ja':
      return ja;
    case 'zh-CN':
      return zhCN;
    case 'zh-TW':
      return zhTW;
    default:
      return en;
  }
};

// 客户端禁止使用，这是sync的方式获取Dictionary； 尽可能另外使用 translate.ts 里的 getDictionary， async的方式
export const getServerToolSDKDictionary = (locale: Locale = 'en'): FormAppDictionary => {
  switch (locale) {
    case 'en':
      return toolsdkEN as FormAppDictionary;
    case 'ja':
      return toolSDKJA;
    case 'zh-CN':
      return toolSDKzhCN;
    case 'zh-TW':
      return toolSDKzhTW;
    default:
      return toolsdkEN;
  }
};

// 服务端快速拿到一个快速的ILocaleContext
export const getServerLocaleContext = (locale: Locale = 'en'): ILocaleContext => {
  const dict = getServerDictionary(locale);
  const t = getTranslations(dict);
  const localeContext = {
    lang: locale,
    t,
    i: (_iStr: iString | undefined) => iStringParse(_iStr, locale),
  };
  return localeContext;
};
