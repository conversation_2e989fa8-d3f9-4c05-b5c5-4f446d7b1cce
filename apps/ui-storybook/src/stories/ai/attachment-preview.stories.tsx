import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import React from 'react';
import { AttachmentPreview } from '@bika/domains/ai/client/chat/utils/attachment-preview';
import type { PreviewAttachment, UploadingAttachment } from '@bika/domains/ai/client/chat/utils/attachment-preview';
import { Box, Stack } from '@bika/ui/layouts';

// Mock data for different attachment types
const mockImageAttachment: PreviewAttachment = {
  id: 'preview-image-1',
  name: 'sample-image.jpg',
  contentType: 'image/jpeg',
  url: 'https://picsum.photos/200/200?random=1',
};

const mockPdfAttachment: PreviewAttachment = {
  id: 'preview-pdf-1',
  name: 'document.pdf',
  contentType: 'application/pdf',
  url: 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
};

const mockDocAttachment: PreviewAttachment = {
  id: 'preview-doc-1',
  name: 'document.docx',
  contentType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  url: 'https://example.com/document.docx',
};

const mockUploadingImageAttachment: UploadingAttachment = {
  id: 1,
  name: 'uploading-image.png',
  contentType: 'image/png',
  base64: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==',
};

const mockUploadingDocAttachment: UploadingAttachment = {
  id: 2,
  name: 'uploading-document.pdf',
  contentType: 'application/pdf',
  base64: '',
};

// Wrapper component to provide context and handle interactions
function AttachmentPreviewWrapper(props: {
  type: 'preview' | 'uploading';
  attachment: PreviewAttachment | UploadingAttachment;
}) {
  return (
    <Box sx={{ p: 2 }}>
      <AttachmentPreview
        data={{
          type: props.type,
          attachment: props.attachment as any,
        }}
      />
    </Box>
  );
}

// Multiple attachments showcase
function MultipleAttachmentsExample() {
  return (
    <Stack direction="row" spacing={2} sx={{ p: 2, flexWrap: 'wrap' }}>
      <AttachmentPreview
        data={{
          type: 'preview',
          attachment: mockImageAttachment,
        }}
      />
      <AttachmentPreview
        data={{
          type: 'preview',
          attachment: mockPdfAttachment,
        }}
      />
      <AttachmentPreview
        data={{
          type: 'preview',
          attachment: mockDocAttachment,
        }}
      />
      <AttachmentPreview
        data={{
          type: 'uploading',
          attachment: mockUploadingImageAttachment,
        }}
      />
      <AttachmentPreview
        data={{
          type: 'uploading',
          attachment: mockUploadingDocAttachment,
        }}
      />
    </Stack>
  );
}

const meta: Meta<typeof AttachmentPreview> = {
  title: '@bika/ai/AttachmentPreview',
  component: AttachmentPreview,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'AttachmentPreview component displays file attachments in AI chat with preview thumbnails and delete functionality.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    data: {
      description: 'Attachment data with type and attachment information',
      control: { type: 'object' },
    },
  },
};

export default meta;

type Story = StoryObj<typeof meta>;

export const ImagePreview: Story = {
  render: () => (
    <AttachmentPreviewWrapper
      type="preview"
      attachment={mockImageAttachment}
    />
  ),
  parameters: {
    docs: {
      description: {
        story: 'Preview of an image attachment with delete button.',
      },
    },
  },
};

export const PdfPreview: Story = {
  render: () => (
    <AttachmentPreviewWrapper
      type="preview"
      attachment={mockPdfAttachment}
    />
  ),
  parameters: {
    docs: {
      description: {
        story: 'Preview of a PDF attachment showing file icon.',
      },
    },
  },
};

export const DocumentPreview: Story = {
  render: () => (
    <AttachmentPreviewWrapper
      type="preview"
      attachment={mockDocAttachment}
    />
  ),
  parameters: {
    docs: {
      description: {
        story: 'Preview of a document attachment showing file icon.',
      },
    },
  },
};

export const UploadingImage: Story = {
  render: () => (
    <AttachmentPreviewWrapper
      type="uploading"
      attachment={mockUploadingImageAttachment}
    />
  ),
  parameters: {
    docs: {
      description: {
        story: 'Uploading state for an image attachment with loading indicator.',
      },
    },
  },
};

export const UploadingDocument: Story = {
  render: () => (
    <AttachmentPreviewWrapper
      type="uploading"
      attachment={mockUploadingDocAttachment}
    />
  ),
  parameters: {
    docs: {
      description: {
        story: 'Uploading state for a document attachment with loading indicator.',
      },
    },
  },
};

export const MultipleAttachments: Story = {
  render: () => <MultipleAttachmentsExample />,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        story: 'Multiple attachments showing different types and states.',
      },
    },
  },
};

// Interactive story with controls
export const Interactive: Story = {
  args: {
    data: {
      type: 'preview',
      attachment: mockImageAttachment,
    },
  },
  argTypes: {
    data: {
      control: {
        type: 'object',
      },
    },
  },
  parameters: {
    docs: {
      description: {
        story: 'Interactive story where you can modify the attachment data through controls.',
      },
    },
  },
};
