import type { Locale } from '@bika/types/i18n/bo';
import HelpDetailPage from './[...slugs]/page';
import { generateMetadata as generateMetadataHelp } from './[...slugs]/page';

async function getParams(): Promise<{ slugs: string[]; lang: Locale }> {
  // default page
  return { slugs: ['tutorials/getting-started'], lang: 'en' };
}

export default async function HelpHomePage() {
  return <HelpDetailPage params={getParams()} />;
}

export async function generateMetadata() {
  const metadata = await generateMetadataHelp({ params: getParams() });
  return metadata;
}
