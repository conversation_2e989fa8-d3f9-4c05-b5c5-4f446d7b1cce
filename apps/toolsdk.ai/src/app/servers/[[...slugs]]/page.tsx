import { PackageSO } from '@toolsdk.ai/domain/server/package-so';
import { PackageDetailVORenderer } from '@toolsdk.ai/domain/client/packages/package-detail-vo-renderer';
import { AuthSO } from '@toolsdk.ai/domain/server/auth-so';
import type { Metadata } from 'next';

interface Props {
  params: Promise<{
    slugs: string[];
  }>;
}
async function getPkg(props: Props) {
  const { slugs } = await props.params;
  let version = '';
  if (slugs[slugs.length - 2] === 'v') {
    version = slugs.pop()!; // 取版本號
    // 移除 v
    slugs.pop();
  }
  const key = decodeURIComponent(slugs.join('/'));

  const pkg = await PackageSO.getByKey(key, version);
  return pkg;
}

export default async function ServerDetailPage(props: Props) {
  const me = await AuthSO.currentUser();
  const pkg = await getPkg(props);

  const vo = await pkg.toDetailVO(me?.id);
  return (
    <>
      <PackageDetailVORenderer value={vo} />
    </>
  );
}

export async function generateMetadata(props: Props) {
  const pkg = await getPkg(props);
  const vo = await pkg.toSimpleVO();
  const metadata: Metadata = {
    title: vo.name,
  };
  return metadata;
}
