'use server';

import { HomePageServer } from '@toolsdk.ai/domain/client/webpages/index/home-page-server';
import type { Metadata } from 'next';

interface Props {
  searchParams: Promise<{ category: string | undefined }>;
}
export default async function MarketplacePage(props: Props) {
  // const sp = await props.searchParams;
  // const kind = sp.kind;
  return <HomePageServer searchParams={props.searchParams} disableCover={true} />;
}

export async function generateMetadata() {
  const metadata: Metadata = {
    title: 'Marketplace',
  };
  return metadata;
}
