{"name": "@bika/mobile", "version": "1.9.0-alpha.22", "dependencies": {"@expo/react-native-action-sheet": "^4.0.1", "@expo/vector-icons": "^14.0.4", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-masked-view/masked-view": "0.3.2", "@react-native-picker/picker": "2.9.0", "@react-native-segmented-control/segmented-control": "2.5.4", "@react-native-voice/voice": "^3.2.4", "@react-navigation/drawer": "^7.1.0", "@react-navigation/native": "^7.0.14", "@rneui/themed": "4.0.0-rc.8", "@shopify/flash-list": "1.7.1", "@tanstack/react-query": "^4.0.0", "@trpc/client": "^10.45.2", "@trpc/react-query": "^10.45.2", "@trpc/server": "^10.45.2", "add": "^2.0.6", "color": "^4.2.3", "date-fns": "^2.30.0", "date-fns-tz": "^2.0.0", "eslint-import-resolver-typescript": "^3.6.1", "expo": "~52.0.18", "expo-application": "~6.0.1", "expo-auth-session": "~6.0.1", "expo-av": "^15.0.1", "expo-camera": "~16.0.9", "expo-checkbox": "~4.0.0", "expo-clipboard": "~7.0.0", "expo-constants": "~17.0.3", "expo-crypto": "~14.0.1", "expo-dev-client": "~5.0.6", "expo-device": "~7.0.1", "expo-document-picker": "~13.0.1", "expo-file-system": "~18.0.5", "expo-font": "~13.0.1", "expo-image": "~2.0.3", "expo-image-picker": "~16.0.3", "expo-linear-gradient": "~14.0.1", "expo-linking": "~7.0.3", "expo-localization": "~16.0.0", "expo-network": "~7.0.3", "expo-notifications": "~0.29.11", "expo-router": "~4.0.11", "expo-secure-store": "^14.0.0", "expo-sharing": "~13.0.0", "expo-splash-screen": "~0.29.18", "expo-status-bar": "~2.0.0", "expo-system-ui": "~4.0.6", "expo-updates": "^0.26.10", "expo-web-browser": "~14.0.1", "i18n-js": "^4.4.3", "jotai": "^2.11.0", "lottie-react-native": "^7.1.0", "nativewind": "^4.0.1", "posthog-react-native": "^3.1.0", "react": "18.3.1", "react-dom": "18.3.1", "react-hook-form": "^7.49.3", "react-native": "0.76.5", "react-native-actions-sheet": "^0.9.3", "react-native-collapsible": "^1.6.1", "react-native-context-menu-view": "^1.15.0", "react-native-date-picker": "^4.4.0", "react-native-file-viewer": "^2.1.5", "react-native-gesture-handler": "~2.20.2", "react-native-loading-spinner-overlay": "^3.0.1", "react-native-markdown-display": "^7.0.2", "react-native-paper": "^5.11.6", "react-native-picker-select": "^9.0.1", "react-native-reanimated": "~3.16.5", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.1.0", "react-native-svg": "15.8.0", "react-native-test-flight": "^1.1.0", "react-native-web": "~0.19.13", "superjson": "^2.2.1", "sweet-sfsymbols": "^0.5.0", "tailwindcss": "^3.4.0", "ts-pattern": "5.0.6"}, "devDependencies": {"@babel/core": "^7.26.0", "@bika/api-caller": "workspace:*", "@bika/contents": "workspace:^", "@bika/domains": "workspace:^", "@bika/types": "workspace:*", "@bika/ui": "workspace:*", "@types/react": "^18.3.16", "@types/react-dom": "^18", "@typescript-eslint/parser": "^7.7.1", "babel-plugin-module-resolver": "^5.0.2", "eas-cli-local-build-plugin": "^1.0.124", "eslint": "^8", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-native": "^4.1.0", "eslint-plugin-tailwindcss": "^3.15.1", "jest": "^29.2.1", "jest-expo": "~52.0.2", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.14", "react-test-renderer": "18.2.0", "typescript": "^5.7.2"}, "jest": {"preset": "jest-expo"}, "main": "index.js", "private": true, "scripts": {"android": "expo run:android", "build": "expo export --output-dir ./build -p ios -p android", "build:postinstall": "pnpm -w build:mobile", "build:prebuild": "npx expo prebuild", "build:development:ios": "eas build -p ios --profile development --clear-cache", "build:development:android": "eas build -p android --profile development --clear-cache", "build:development:local:android": "eas build -p android --profile development --local --clear-cache", "build:development:local:ios": "eas build -p ios --profile development --local --clear-cache", "build:development:local:ios:non-interaction": "eas build -p ios --profile development --local --non-interactive --clear-cache", "build:production:ios": "eas build -p ios --clear-cache", "build:production:android": "eas build -p android --clear-cache", "build:production:local:android": "eas build -p android --local --clear-cache", "build:production:local:ios": "eas build -p ios --local --clear-cache", "build:production:local:ios:non-interaction": "eas build -p ios --local --non-interactive --clear-cache", "deps:check": "npx expo install --check", "deps:fix": "npx expo install --fix", "dev": "expo start", "dev:android": "expo start --android", "dev:ios": "expo start --ios", "dev:web": "expo start --web", "expo:doctor": "npx expo-doctor", "ios": "expo run:ios", "check": "tsc --noEmit", "lint": "eslint --quiet --ext js,ts,tsx ./app ./components", "lint:fix": "eslint --ext js,ts,tsx ./app ./components --fix", "start": "expo start", "submit:android": "eas submit --platform android --latest", "submit:ios": "eas submit --platform ios --latest", "test": "jest --passWithNoTests", "test-watch": "jest --watchAll"}}