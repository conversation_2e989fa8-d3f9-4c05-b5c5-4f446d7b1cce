import type { ToolInvocation } from '@ai-sdk/ui-utils';
import { throttle } from 'lodash';
import { useEffect, useRef, useCallback } from 'react';
import type { SkillsetSelectDTO } from '@bika/types/skill/dto';
import { Markdown } from '@bika/ui/markdown';
import { ArtifactContainer } from '../../ai/client/chat/artifacts/components/artifact-container';

interface MarkdownArtifactProps {
  content: string;
  skillsets: SkillsetSelectDTO[];
  tool: ToolInvocation;
  data: string;
}

export const MarkdownArtifact = (props: MarkdownArtifactProps) => {
  const { content, skillsets, tool, data } = props;
  const containerRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = useCallback(
    throttle(() => {
      if (containerRef.current) {
        containerRef.current.scrollTop = containerRef.current.scrollHeight;
      }
    }, 100),
    [],
  );

  useEffect(() => {
    scrollToBottom();
  }, [content, scrollToBottom]);

  useEffect(
    () => () => {
      scrollToBottom.cancel();
    },
    [scrollToBottom],
  );

  return (
    <ArtifactContainer data={data} skillsets={skillsets} tool={tool} rowDataType="markdown">
      <div
        ref={containerRef}
        className="overflow-auto p-[16px] m-2"
        style={{
          overflow: 'auto',
        }}
      >
        <Markdown markdown={content} />
      </div>
    </ArtifactContainer>
  );
};
