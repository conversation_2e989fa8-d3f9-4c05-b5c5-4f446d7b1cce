import assert from 'assert';
import { updateToolCallResult } from '@ai-sdk/ui-utils';
import { type DataStreamWriter } from 'ai';
import { generateNanoID } from 'sharelib/nano-id';
import { errors, ServerError } from '@bika/contents/config/server/error';
import { type Locale } from '@bika/contents/i18n';
import { MemberSO } from '@bika/domains/unit/server/member-so';
import { UserSO } from '@bika/domains/user/server/user-so';
import { db, AIChatModel, AIMessageModel, AIToolModel } from '@bika/server-orm';
import { AIIntentParams, AIMessageBO, AIUsage, AIMessagePartToolInvocation, toUIMessages } from '@bika/types/ai/bo';
import { AIIntentUIResolveDTO, ListWizardDTO } from '@bika/types/ai/dto';
import { AIMessageVO, AIWizardVO, IntentResolutionStatus, AIResolveVO, AIWizardSimpleVO } from '@bika/types/ai/vo';
import { CONST_PREFIX_WIZ, CONST_PREFIX_AI_MESSAGE } from '@bika/types/database/vo';
import type { SkillsetSelectDTO } from '@bika/types/skill/dto';
import { iStringParse } from '@bika/types/system';
import { ApiFetchRequestContext } from '@bika/types/user/vo';
import { IntentSO } from './intent-so';
import { getArtifactIdsFromToolInvocation, ResolutionResultInfo } from '../types';
import { AIMessageSO } from './ai-message-so';
import { AIArtifactSO } from '../../../ai-artifacts/ai-artifact-so';
import { AISkillsetServerRegistry } from '../../../ai-skillset/server-registry';
import type { SkillsetHandlerContext } from '../../../ai-skillset/types';

export type IResolveResult = { message: AIMessageVO; intent: IntentSO };

export class AIChatSO {
  private _model: AIChatModel;

  private _intent: IntentSO;

  private _user: UserSO;

  private constructor(model: AIChatModel, intent: IntentSO, user: UserSO) {
    this._model = model;
    this._intent = intent;
    this._user = user;
  }

  public async getMessages(): Promise<AIMessageBO[]> {
    const messagesPOs = await this.getMessagesPOs();
    const chatHistories: AIMessageBO[] = messagesPOs
      .filter((i) => i.message?.parts?.length)
      .map((po) => po.message as AIMessageBO);
    return chatHistories;
  }

  /**
   * Chat ID
   */
  public get id() {
    return this._model.id;
  }

  async getMessagesPOs(): Promise<AIMessageModel[]> {
    const aiMessagesPOs = await db.mongo
      .aiMessage(this.id)
      .find({
        chatId: this._model.id,
      })
      .sort({ createdAt: 1 }); // 1 表示升序，-1 表示降序;
    return aiMessagesPOs;
  }

  public get user() {
    return this._user;
  }

  public get model() {
    return this._model;
  }

  public get intent(): IntentSO {
    return this._intent;
  }

  public get intentResolutionStatus() {
    return this._intent.resolutionStatus;
  }

  static async init(id: string) {
    const wizardPO = await db.mongo.aiChat.findOne({
      id,
    });
    if (!wizardPO) {
      throw new Error(`AIWizard not found: ${id}`);
    }
    const user = await this.initializeUser(wizardPO);
    const intent = await IntentSO.create(
      wizardPO,
      wizardPO.intent,
      wizardPO.intentResolutionStatus as IntentResolutionStatus,
      user,
    );
    return new AIChatSO(wizardPO, intent, user);
  }

  // 获取chat的user
  // private async getUser(): Promise<UserSO> {
  //   let user;
  //   switch (this._model.roleType) {
  //     case 'MEMBER':
  //       {
  //         const member = await this.getHumanMember();
  //         user = member?.getUser();
  //       }
  //       break;
  //     case 'USER':
  //       user = await UserSO.init(this._model.roleId);
  //       break;
  //     default:
  //       throw new Error(`Unknown AIWizard roleType: ${this._model.roleType}`);
  //   }
  //   return user;
  // }

  public async resolve(
    ctx: ApiFetchRequestContext,
    resolveObj: AIResolveVO,
    forceLocale?: Locale,
    dataStreamWriter?: DataStreamWriter,
  ): Promise<IResolveResult> {
    if (resolveObj.type === 'UI' && !resolveObj.message) {
      // ensure the message not null (debt)
      console.warn(`[WARN]resolveObj.message is undefined`);
      resolveObj.message = `/resolve:${JSON.stringify(resolveObj.uiResolve)}`;
    }
    if (this.isEnd()) {
      throw new Error(`Dialog已经结束，不允许再聊天了: ${this._model.id}`);
    }
    const user = ctx.session?.userId ? await UserSO.init(ctx.session.userId) : await this._user;

    const { locale } = ctx;
    // 这里对内存中的对话，添加了聊天记录
    const messagesPOs = await this.getMessagesPOs();
    const chatHistories: AIMessageBO[] = messagesPOs.map((po) => po.message as AIMessageBO);
    //  先保存人类消息, 防止服务端还未执行完，用户刷新网页之后 消息丢失, 会更新chatHistories最后一条消息
    await this.doSaveHumanMessage(chatHistories, resolveObj, user.id);
    await this.checkAICredit();

    // if (resolveObj.type === 'TOOL') {
    //   // 如果是 tool resolve， 替换最后一条消息（含了 tool-result 执行结果)，在 tool 里加入 invocation
    //   // const lastMessage = chatMessages[chatMessages.length - 1];
    //   chatMessages[chatMessages.length - 1] = resolveObj.message;
    //   // console.log('Tool Resolve', resolveObj.uiMessage);
    // }

    const gen = await this._intent.resolveYield(
      ctx,
      resolveObj,
      forceLocale || locale,
      chatHistories,
      user,
      dataStreamWriter,
    );

    const resolution: ResolutionResultInfo = gen.resolution;
    const intent = gen.intent;

    assert(resolution);
    assert(intent);

    this._intent = intent!; // This IntentSO maybe be a new intent

    const { message: saveMessageBO } = await this.doSaveAiResponse(
      resolution,
      intent,
      // chatHistories,
      user.id,
    );

    const saveMessageVO: AIMessageVO = {
      ...saveMessageBO,
      // creator: user.toVO(),
    };

    return {
      message: saveMessageVO,
      intent,
    };
  }

  async getHumanMember(): Promise<MemberSO> {
    return MemberSO.init(this._model.roleId);
  }

  /**
   * 当intent已经完成了，就是结束Wizard Dialog，不让继续聊天了
   *
   * @returns
   */
  isEnd(): boolean {
    return this.intentResolutionStatus === 'SUCCESS';
  }

  private static async initializeUser(wizardPO: { roleType: 'MEMBER' | 'USER'; roleId: string }): Promise<UserSO> {
    let user;
    switch (wizardPO.roleType) {
      case 'MEMBER':
        {
          // const member = await this.getHumanMember();
          const member = await MemberSO.init(wizardPO.roleId);
          user = member?.getUser();
        }
        break;
      case 'USER':
        user = await UserSO.init(wizardPO.roleId);
        break;
      default:
        throw new Error(`Unknown AIWizard roleType: ${wizardPO.roleType}`);
    }
    return user;
  }

  static async get(wizardId: string) {
    const wizardPO = await db.mongo.aiChat.findOne({
      id: wizardId,
    });

    if (!wizardPO) {
      throw new ServerError(errors.ai.chat_not_found);
    }

    const user = await this.initializeUser(wizardPO!);

    const intent = await IntentSO.create(
      wizardPO!,
      wizardPO!.intent,
      wizardPO!.intentResolutionStatus as IntentResolutionStatus,
      user,
    );

    return new AIChatSO(wizardPO!, intent, user);
  }

  static async create(roleType: 'MEMBER' | 'USER', roleId: string, intent: AIIntentParams) {
    const initResolutionStatus = 'DIALOG';
    const user = await this.initializeUser({ roleType, roleId });
    const newChatModel: AIChatModel = {
      id: generateNanoID(CONST_PREFIX_WIZ),
      intent,
      intentResolutionStatus: initResolutionStatus, // 这里上面AI产生了对话，默认就是对话过，客户端收到后，就不会主动发起对话
      roleType,
      roleId,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: user.id,
      updatedBy: user.id,
    };

    const intentSO = await IntentSO.create(newChatModel, intent, initResolutionStatus, user);
    const prologue = await intentSO.getPrologue();

    // 开场白也算一条message
    if (prologue) {
      const initMsg: AIMessageBO = {
        id: generateNanoID(CONST_PREFIX_AI_MESSAGE),
        role: 'assistant',
        ...prologue,
      };
      // {
      //   id: generateNanoID(CONST_PREFIX_AI_MESSAGE),
      //   role: 'assistant',
      //   content: '',
      //   ...prologue,
      //   // parts: prologue.parts,
      //   // voice: prologue.voice,
      //   // ui: prologue.ui,
      //   // prompts: prologue.prompts,
      // };

      // newWizardModel.messages.push(initMsg);

      const newMessagePOs: AIMessageModel[] = [];
      newMessagePOs.push({
        id: initMsg.id, // generateNanoID(CONST_PREFIX_AI_MESSAGE),
        chatId: newChatModel.id,
        message: initMsg,
        usages: undefined,
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: user.id,
        updatedBy: user.id,
      });

      await db.mongo.aiMessage(newChatModel.id).insertMany(newMessagePOs);
    }

    const newWizardPO = await db.mongo.aiChat.create(newChatModel);

    return new AIChatSO(newWizardPO, intentSO, user);
  }

  async delete() {
    await db.mongo.aiChat.deleteOne({
      id: this._model.id,
    });
  }

  async toDetailVO() {
    return {
      messages: await this.getMessages(),
      intent: this._model.intent,
    };
  }

  public async getLastHumanMessage() {
    const messages = (await this.getMessages()) as AIMessageBO[];
    for (let i = messages.length - 1; i >= 0; i -= 1) {
      // 倒序查找
      if (messages[i].role === 'user') {
        return messages[i];
      }
    }
    return null;
  }

  public async getLastAIMessage() {
    const messages = await this.getMessages();
    for (let i = messages.length - 1; i >= 0; i -= 1) {
      // 倒序查找
      if (messages[i].role === 'assistant') {
        return messages[i];
      }
    }
    return null;
  }

  /**
   * Only the last messages
   *
   * @returns
   */
  async toVO(): Promise<AIWizardVO> {
    // const lastAiMessage = this.getLastAIMessage();
    // const lastHumanMessage = this.getLastHumanMessage();
    const wizardVO: AIWizardVO = {
      id: this._model.id,
      title: this._intent.title,
      intent: this._intent.model,
      messages: await AIMessageSO.convertBOToVO(await this.getMessages()),
      // lastAiMessage,
      // lastHumanMessage,
      // intentUI: await this._intent.parseUI(user),
      resolutionStatus: this.intentResolutionStatus,
      options: await this._intent.getOptions(),
    };
    return wizardVO;
  }

  async toSimpleVO(): Promise<AIWizardSimpleVO> {
    const messages = await this.getMessages();
    const title = messages.find((m) => m.role === 'user')?.parts!.findLast((p) => p.type === 'text')?.text;

    const description = messages
      .find((m, index) => m.role === 'assistant' && index > 0)
      ?.parts!.findLast((p) => p.type === 'text')?.text;

    return {
      id: this._model.id,
      createdAt: new Date(this._model.createdAt).toISOString(),
      title: iStringParse(title),
      description: iStringParse(description),
      resolutionStatus: this.intentResolutionStatus,
      creator: this.user?.toVO(),
    };
  }

  // public newMessage(message: string, type: AIMessageType = AIMessageType.TEXT): AIMessageSO {
  // }

  /**
   * 语音输入！传入附件ID
   * @param attachmentId
   */
  // public async humanSpeak(attachmentId: string) {
  //   // TODO: 语音解释成文字并做处理
  //   return this.humanSay(`语音附件${attachmentId}`);
  // }

  /**
   * 以UI的方式按了按钮
   * @param arg0
   */
  // async onResolveUI(resolveObj: AIResolveVO): Promise<{ message: AIMessageBO, intent: IntentSO }> {
  //   const resolveUI = AIResolveUIVOSchema.parse(resolveObj);
  //   if (this.isEnd()) {
  //     throw new Error(`Dialog已经结束，不允许再聊天了: ${this._model.id}`);
  //   }

  //   const { intent: intentSO, resolution } = await this._intent.resolve(resolveUI);
  //   this._intent = intentSO; // This IntentSO maybe be a new intent
  //   return this.doSaveAiResponse(resolution, intentSO);
  // }
  async doSaveHumanMessage(chatHistories: AIMessageBO[], resolveObj: AIResolveVO, userId: string) {
    if (resolveObj.type === 'MESSAGE') {
      const newMessage: AIMessageBO = {
        id: generateNanoID(CONST_PREFIX_AI_MESSAGE),
        role: 'user',
        content: resolveObj.message,
        parts: [
          {
            type: 'text',
            text: resolveObj.message,
          },
        ],
        createdBy: userId,
        experimental_attachments: resolveObj.attachments,
      };
      await this.doSaveHumanNewMessages([newMessage], userId);
      chatHistories.push(newMessage);
    }

    if (resolveObj.type === 'TOOL' && resolveObj.toolInvocation) {
      assert(resolveObj.toolInvocation.state === 'result', 'Tool Invocation should be in result state');
      // 更新 tool call 的状态，把 state call 改成 result
      const msgs = toUIMessages(chatHistories);
      updateToolCallResult({
        messages: msgs,
        toolCallId: resolveObj.toolInvocation.toolCallId,
        toolResult: resolveObj.toolInvocation.result,
      });
      const lastMsg = msgs[msgs.length - 1];

      const updatedMsgPO = await db.mongo.aiMessage(this.id).updateOne(
        {
          id: lastMsg.id,
          chatId: this.id,
        },
        {
          $set: {
            message: lastMsg,
          },
        },
      );

      if (
        typeof resolveObj.toolInvocation.result === 'object' &&
        'error' in resolveObj.toolInvocation.result &&
        resolveObj.toolInvocation.result.error
      ) {
        await db.mongo.aiArtifact.updateOne(
          {
            toolCallId: resolveObj.toolInvocation.toolCallId,
          },
          {
            $set: {
              state: 'ERROR',
              error: resolveObj.toolInvocation.result.error,
            },
          },
        );
      }
      assert(updatedMsgPO, `AI Message not updated: ${lastMsg.id}`);
      chatHistories.splice(-1, 1, lastMsg);
    }
  }

  private async doSaveHumanNewMessages(messages: AIMessageBO[], userId: string) {
    const humanMessagePOs: AIMessageModel[] = messages.map((message) => ({
      id: message.id, // generateNanoID(CONST_PREFIX_AI_MESSAGE),
      chatId: this._model.id,
      message,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: userId,
      updatedBy: userId,
    }));
    await db.mongo.aiMessage(this.id).insertMany(humanMessagePOs);
  }

  /**
   * DB Upsert Tool Invocation
   *
   * @param msgId
   * @param toolInvocation
   * @returns
   */
  private async upsertToolInvocation(
    msgId: string,
    resolution: ResolutionResultInfo,
    toolInvocation: AIMessagePartToolInvocation,
    skillsets: SkillsetSelectDTO[] | undefined = undefined,
  ) {
    let aiToolPO = await db.mongo.aiTool.findOneAndUpdate(
      { toolCallId: toolInvocation.toolCallId },
      {
        chatId: this._model.id, // chatId 用于索引，避免 toolCallId 重复
        messageId: msgId,
        state: toolInvocation.state,
        toolCallId: toolInvocation.toolCallId,
        toolName: toolInvocation.toolName,
        data: toolInvocation,
        prompt: resolution.prompt,
        options: resolution.options,
        skillsets,
      },
    );
    if (!aiToolPO) {
      aiToolPO = await db.mongo.aiTool.create({
        id: generateNanoID('ait'),
        chatId: this._model.id,
        messageId: msgId,
        toolCallId: toolInvocation.toolCallId,
        toolName: toolInvocation.toolName,
        state: toolInvocation.state,
        data: toolInvocation,
        prompt: resolution.prompt,
        options: resolution.options,
        skillsets,
      });
    }
    assert(aiToolPO, `AI Tool Invocation not created: ${msgId} ${JSON.stringify(toolInvocation.toolCallId, null, 2)}`);
    return aiToolPO;
  }

  public static async executeTool(
    index: {
      toolCallId: string;
      chatId?: string; // 可选，避免 toolCallId 重复，找错
    },
    // toolName: string,
    // skillsetsSelects: SkillsetSelectDTO[],
    context: SkillsetHandlerContext,
  ) {
    const { toolCallId } = index;
    const indexFinder = index.chatId ? { toolCallId, chatId: index.chatId } : { toolCallId };

    const aiToolPO: AIToolModel | null = await db.mongo.aiTool.findOne(indexFinder);
    assert(aiToolPO, `Tool Model not found: ${JSON.stringify(indexFinder)}`);

    const aiToolInvocation = aiToolPO.data as AIMessagePartToolInvocation;
    assert(aiToolInvocation.state === 'call', `Tool Invocation is not in call state: ${toolCallId}`);
    const toolName = aiToolPO.toolName;

    const skillsetsSelects = (aiToolPO.skillsets || []) as SkillsetSelectDTO[];

    const toolset = await AISkillsetServerRegistry.parseAISDKToolsets(skillsetsSelects, context, true);
    const tool = toolset[toolName];
    if (!tool) {
      console.error(`Tool ${toolName} not found in skillsets`, skillsetsSelects);
      return null;
    }
    if (!tool.execute) {
      console.error(`Tool ${toolName} has no execute function`, skillsetsSelects);
      return null;
    }
    const result = tool.execute(aiToolInvocation.args, { toolCallId, messages: [] });
    return result;
  }

  /**
   *  持久化
   *
   * @param humanSay
   * @param aiResolution
   * @param intentSO
   * @param userId
   * @returns
   */
  async doSaveAiResponse(
    aiResolution: ResolutionResultInfo,
    intentSO: IntentSO,
    // chatHistories: AIMessageBO[],
    userId: string,
  ) {
    assert(typeof aiResolution === 'object');

    const newAIMsgId = generateNanoID(CONST_PREFIX_AI_MESSAGE);
    const aiResMsg = aiResolution.message;

    assert(aiResMsg);

    // 处理字段赋值，因为 ai response 里的 annotations 字段加入了skillsets，所以需要从 annotations 里获取 skillsets
    const skillsets = aiResMsg.annotations?.find((a) => a.type === 'skillsets')?.skillsets;

    // Tool Invocation 处理，保存
    for (const part of aiResMsg.parts || []) {
      if (part.type === 'tool-invocation' && part.toolInvocation) {
        // 记录 tool invocation 的调用 ID，对应的数据库
        if (part.toolInvocation.state === 'call') {
          console.log(
            '新的 tool call',
            part.toolInvocation.toolCallId,
            part.toolInvocation.toolName,
            part.toolInvocation.state,
            part.toolInvocation.args,
            skillsets,
          );
        } else if (part.toolInvocation.state === 'result') {
          // 取数据库寻找对应的 tool call id 的tool invocation，标记 result （有可能不存在，因为有些 tool 是服务端运行直接到结果到 message 里）
          console.log(
            '新的 tool result',
            part.toolInvocation.toolCallId,
            part.toolInvocation.toolName,
            part.toolInvocation.state,
            part.toolInvocation.args,
            part.toolInvocation.result,
            skillsets,
          );
        } else {
          console.log('TODO: 新的 tool invocation 状态', part.toolInvocation.state);
        }
        await this.upsertToolInvocation(newAIMsgId, aiResolution, part.toolInvocation, skillsets);
      }
    }

    // const responseAIMsgs = [];

    // for (const aiResMsg of resMessages) {
    const newAISay: AIMessageBO = {
      id: newAIMsgId,
      role: 'assistant',
      // content: aiResMsg.content || '',
      ...aiResMsg,
    };
    // console.log('newAISay', JSON.stringify(newAISay, null, 2));

    // if (newHumanSay) {
    //   newMsgs.push(newHumanSay);
    // }
    // if (newAISay) {
    //   responseAIMsgs.push(newAISay);
    // }
    // }

    const intentParams = intentSO.toVO();

    // const newMsgs = [...chatHistories, newAISay];
    const wizardPO = await db.mongo.aiChat.updateOne(
      {
        id: this._model.id,
      },

      {
        $set: {
          // messages: newMsgs,
          intent: intentParams,
          intentResolutionStatus: intentSO.resolutionStatus,
          updatedBy: userId,
        },
      },
    );
    assert(wizardPO.matchedCount);

    // collect artifact usages
    const artifactUsages = await this.getAIArtifactUsages(newAISay);
    const usages = [...(aiResolution.usage ? [aiResolution.usage] : []), ...artifactUsages];
    const aiMessagePO = {
      id: newAISay.id,
      chatId: this._model.id,
      message: newAISay,
      usages,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: userId,
      updatedBy: userId,
    };
    // space 扣除 usage
    await this.costAICredit(this._model.id, newAIMsgId, usages, userId);
    let updatedPO = await db.mongo
      .aiMessage(this.id)
      .findOneAndUpdate({ id: newAISay.id, chatId: this.id }, aiMessagePO);
    if (!updatedPO) {
      updatedPO = await db.mongo.aiMessage(this.id).insertOne(aiMessagePO);
    }
    assert(updatedPO, `[doSaveAiResponse] AI Message not updated or created : ${newAISay.id}`);

    return {
      message: newAISay,
      intent: intentSO,
    };
  }

  public get roleType() {
    return this._model.roleType;
  }

  public get roleId() {
    return this._model.roleId;
  }

  async getAIArtifactUsages(message: AIMessageBO): Promise<AIUsage[]> {
    const artifactIds = getArtifactIdsFromToolInvocation(message);
    const usages = await AIArtifactSO.getUsagesByIds(artifactIds);
    return usages;
  }

  async costAICredit(chatId: string, msgId: string, usages: AIUsage[], userId: string) {
    const costCredit: number = usages.reduce((acc, curr) => acc + curr.costCredit, 0);
    if (this.roleType === 'MEMBER') {
      const member = await this.getHumanMember();
      const space = await member.getSpace();
      const coinsAccount = await space.billing.getCoinsAccount();
      await coinsAccount.redeem(
        costCredit,
        {
          reason: 'cost-ai-chat-credit',
          chatId,
          messageId: msgId,
        },
        undefined,
        undefined,
        userId,
      );
    } else if (this.roleType === 'USER') {
      const user = await UserSO.init(this.roleId);
      const coinsAccount = await user.coins.getAccount();
      await coinsAccount.redeem(
        costCredit,
        {
          reason: 'cost-ai-chat-credit',
          chatId,
          messageId: msgId,
        },
        undefined,
        undefined,
        userId,
      );
    } else {
      throw new Error(`Unknown AIChat roleType: ${this._model.roleType}`);
    }
  }

  async checkAICredit() {
    let enough = true;
    if (this.roleType === 'MEMBER') {
      const member = await this.getHumanMember();
      const space = await member.getSpace();
      const coinsAccount = await space.billing.getCoinsAccount();
      enough = await coinsAccount.enough(1);
    }
    if (this.roleType === 'USER') {
      const user = await UserSO.init(this.roleId);
      const coinsAccount = await user.coins.getAccount();
      enough = await coinsAccount.enough(1);
    }
    if (!enough) {
      throw new ServerError(errors.usage.ai_credit_not_enough);
    }
  }

  /**
   * Human Message Resolve，区别是，UIResolve直接调用UI，而Message Resolve是需要组装上下文聊天历史的
   *
   * @param resolveObj
   * @returns
   */
  public async message(
    ctx: ApiFetchRequestContext,
    message: string,
  ): Promise<{ message: AIMessageBO; intent: IntentSO }> {
    const resolveObj: AIResolveVO = {
      type: 'MESSAGE',
      message,
    };
    return this.resolve(ctx, resolveObj);
  }

  public async ui(ctx: ApiFetchRequestContext, uiResolve: AIIntentUIResolveDTO) {
    const resolveObj: AIResolveVO = {
      type: 'UI',
      uiResolve,
    };
    return this.resolve(ctx, resolveObj);
  }

  static async getWizardIdByNodeId(nodeId: string): Promise<string | undefined> {
    // according intent.nodeId, find the wizard.id
    const wizard = await db.mongo.aiChat.findOne(
      {
        'intent.nodeId': nodeId,
      },
      {
        _id: 0,
        id: 1,
      },
      {
        sort: {
          createdAt: -1,
        },
      },
    );
    return wizard?.id;
  }

  private static pasreListWizardDTOToQuery(param: ListWizardDTO, user?: UserSO) {
    const { type } = param;

    let query = {};
    switch (type) {
      case 'AI_COPILOT':
        query = {
          'intent.type': 'COPILOT',
          'intent.copilot.nodeId': param.nodeId,
        };
        break;
      case 'AI_NODE':
        query = {
          'intent.type': 'AI_NODE',
          'intent.nodeId': param.nodeId,
        };
        break;
      case 'AI_BUILDER':
        query = {
          'intent.type': 'BUILD_APP',
          'intent.spaceId': param.spaceId,
          createdBy: user?.id,
        };
        break;
      case 'AI_SUPERVISOR':
        query = {
          'intent.type': 'DEBUGGER',
        };
        break;
      default:
        query = {};
        break;
    }
    return query;
  }

  static async getWizardList(user: UserSO, param: ListWizardDTO): Promise<AIChatSO[]> {
    const { pageNo, pageSize } = param;
    const query = this.pasreListWizardDTOToQuery(param, user);
    const wizards = await db.mongo.aiChat.find(query, undefined, {
      sort: {
        createdAt: -1,
      },
      skip: (pageNo - 1) * pageSize,
      limit: pageSize,
    });
    if (!wizards.length) {
      return [];
    }
    const createdByIds = [...new Set(wizards.filter((w) => w.roleType === 'USER' && w.roleId).map((w) => w.roleId))];
    const userMap = createdByIds.length ? await UserSO.buildMapByIds(createdByIds) : {};
    return wizards.map((w) => {
      const userInfo = userMap[w.roleId]!;
      const intent = new IntentSO(w, w.intent, w.intentResolutionStatus as IntentResolutionStatus, userInfo);
      return new AIChatSO(w, intent, userInfo);
    });
  }

  static async countWizard(user: UserSO, param: ListWizardDTO): Promise<number> {
    const query = this.pasreListWizardDTOToQuery(param, user);
    return db.mongo.aiChat.countDocuments(query);
  }
}
