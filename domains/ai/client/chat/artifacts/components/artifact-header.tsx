import type { ToolInvocation } from '@ai-sdk/ui-utils';
import React from 'react';
import type { SkillsetSelectDTO } from '@bika/types/skill/dto';
import { useGlobalContext } from '@bika/types/website/context';
import { useAIArtifactGlobalStore } from '../../../../../website-global-modals/ai-artifact-global-store';
import { IconButton } from '@bika/ui/icon-button';
import ExpandOutlined from '@bika/ui/icons/components/expand_outlined';
import NarrowOutlined from '@bika/ui/icons/components/narrow_outlined';
import { Stack } from '@bika/ui/layouts';
import { DataViewToggle, DataViewToggleProps, DataViewVariant } from './data-view-toggle';
import { useModal } from '../../../../../website/client/context/global/hooks/useModal';

export interface ArtifactHeaderProps<T extends object | string> {
  switchProps?: Partial<DataViewToggleProps>;
  expandable?: boolean;
  toolbarButton?: React.ReactElement;
  viewMode?: DataViewVariant;

  skillsets: SkillsetSelectDTO[];
  onViewModeChange: (variant: DataViewVariant) => void;

  data: T;
  // pass through
  tool?: ToolInvocation;
  /**
   * Optional className for custom styling
   */
  className?: string;

  rowDataOnly?: boolean;
}

/**
 * A header component for artifacts that wraps the DataViewToggle component.
 * Provides a consistent header layout with optional title and additional content.
 */
export const ArtifactHeader: React.FC<ArtifactHeaderProps<object | string>> = ({
  className,
  tool,
  switchProps: dataViewToggleProps,
  skillsets,
  toolbarButton,
  rowDataOnly,
  expandable = true,
  viewMode,
  onViewModeChange,
}) => {
  const initialMode = rowDataOnly ? 'rowData' : 'preview';
  const checkViewMdoe = viewMode ?? initialMode;
  const modalControl = useModal();

  const globalContext = useGlobalContext();

  const isModal = modalControl.showUIModal?.name === 'AI_ARTIFACT';

  const { setData: setGlobalArtifact } = useAIArtifactGlobalStore();

  return (
    <div
      className={`artifact-header ${className || ''}`}
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        // padding: '0 16px',
        // backgroundColor: 'var(--bg-surface, #ffffff)',
        minHeight: '32px',
        flex: '0 0 32px',
      }}
    >
      <DataViewToggle
        {...dataViewToggleProps}
        rowDataOnly={rowDataOnly}
        variant={checkViewMdoe}
        onChange={onViewModeChange}
      />

      <Stack alignItems={'center'} direction={'row'}>
        {!isModal && expandable !== false && (
          <IconButton
            variant="plain"
            size="sm"
            color="neutral"
            onClick={() => {
              console.log('to', skillsets);
              if (tool) {
                setGlobalArtifact({
                  message: { id: '', role: 'assistant', content: '' },
                  tool,
                  skillsets,
                });
                globalContext.showUIModal({ name: 'AI_ARTIFACT' });
              }
            }}
            sx={{
              '&:hover': {
                backgroundColor: 'var(--hover)',
              },
            }}
          >
            <ExpandOutlined color="text-primary" />
          </IconButton>
        )}

        {toolbarButton}

        {isModal && (
          <IconButton
            variant="plain"
            size="sm"
            color="neutral"
            onClick={() => {
              globalContext.showUIModal(null);
            }}
            sx={{
              '&:hover': {
                backgroundColor: 'var(--hover)',
              },
            }}
          >
            <NarrowOutlined color="text-primary" />
          </IconButton>
        )}
      </Stack>
    </div>
  );
};
