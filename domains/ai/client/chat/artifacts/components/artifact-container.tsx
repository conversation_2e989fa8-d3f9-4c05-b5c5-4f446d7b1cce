import dynamic from 'next/dynamic';
import React, { useState } from 'react';
import { Box, Stack } from '@bika/ui/layouts';
import { ArtifactHeader, ArtifactHeaderProps } from './artifact-header';
import { DataViewVariant } from './data-view-toggle';

const mappedContent = {
  markdown: (text: unknown) => text?.toString(),
  html: (text: unknown) => text?.toString(),
  json: (_data: unknown) => JSON.stringify(_data, null, 2),
};

const mappedTitle = {
  markdown: 'Markdown',
  json: 'Json',
  html: 'Html',
  doc: 'Document',
};
export interface ArtifactContainerProps<T extends object | string>
  extends Omit<ArtifactHeaderProps<T>, 'viewMode' | 'onViewModeChange'> {
  children?: React.ReactNode;
  rowDataType?: 'json' | 'markdown' | 'html';

  /**
   * When true, the container will default to showing raw data view
   */
  rowDataOnly?: boolean;
}

const CodeViewWithHeader = dynamic(() => import('@bika/ui/code-view').then((module) => module.CodeViewWithHeader), {
  loading: () => <></>,
  ssr: false,
});

/**
 * A container component for artifacts that combines ArtifactHeader with content display.
 * Provides view mode switching between preview and raw data views.
 */
export const ArtifactContainer: React.FC<ArtifactContainerProps<object | string>> = ({
  children,
  className: _className,
  tool,
  expandable,
  skillsets,
  data,
  switchProps: dataViewToggleProps,
  toolbarButton,
  rowDataOnly,
  rowDataType,
}) => {
  const initialMode = rowDataOnly ? 'rowData' : 'preview';
  const [viewMode, setViewMode] = useState<DataViewVariant>(initialMode);

  const jsonText = JSON.stringify(data, null, 2);
  const handleViewModeChange = (variant: DataViewVariant) => {
    setViewMode(variant);
  };

  return (
    <Stack className="w-full flex flex-col h-full gap-[16px]">
      <ArtifactHeader
        data={data}
        skillsets={skillsets}
        tool={tool}
        switchProps={dataViewToggleProps}
        toolbarButton={toolbarButton}
        expandable={expandable}
        rowDataOnly={rowDataOnly}
        viewMode={viewMode}
        onViewModeChange={handleViewModeChange}
      ></ArtifactHeader>

      {/* pt-[16px] */}
      <Box
        className="w-full flex flex-1  overflow-hidden rounded-lg"
        sx={{
          border: '1px solid var(--border-default)',
          backgroudColor: 'var(--bg-controls)',
        }}
      >
        {viewMode === 'rowData' && (
          <CodeViewWithHeader
            title={mappedTitle[rowDataType ?? 'json']}
            lang={rowDataType ?? 'json'}
            code={mappedContent[rowDataType ?? 'json']?.(data) ?? jsonText}
          />
        )}
        {viewMode === 'preview' && (
          <Stack
            direction="column"
            className="w-full w-full"
            sx={{
              backgroundColor: 'var(--bg-page)',
            }}
          >
            {children}
          </Stack>
        )}
      </Box>
    </Stack>
  );
};
