// import dayjs from 'dayjs';
import { expect, test } from 'vitest';
import { MockContext } from '@bika/domains/__tests__/mock/mock.context';
// import { Searcher } from '@bika/server-orm';
// import { AuditLog } from '@bika/types/system';
// import { TrackLog } from '@bika/types/system/track';

// export const testSearchIndex = async (client: Searcher) => {
export const testSearchIndex = async () => {
  const { user, space } = await MockContext.initUserContext();

  test('should have 0 track logs', async () => {
    expect(1).toEqual(1);
  });

  // 创建 TrackLog
  // const now = dayjs();
  // // 47 小时前
  // const hoursAgo47 = now.subtract(47, 'hour').toISOString();

  // // 23 小时前
  // const hoursAgo23 = now.subtract(23, 'hour').toISOString();

  // // 创建 47 小时前的 Track Log 和 Audit Log
  // const trackLog1: TrackLog = {
  //   kind: 'TRACK_LOG',
  //   type: 'SSE',
  //   sse: {
  //     name: 'online-user',
  //     userId: user.id,
  //     route: 'space',
  //     onlineSessionId: '123',
  //     user: user.toVO(),
  //   },
  //   userId: user.id,
  //   // createdAt: hoursAgo47,
  // };
  // const auditLog1: AuditLog = {
  //   kind: 'ACCESS_LOG',
  //   action: {
  //     type: 'trpc',
  //     path: '/space',
  //   },
  //   headers: {},
  //   client: {
  //     hostname: '127.0.0.1',
  //     ip: '127.0.0.1',
  //     version: '0.0.1',
  //   },
  //   server: {
  //     hostname: '127.0.0.1',
  //     platform: 'Windows',
  //   },
  // };

  // // 创建 23 小时前的 Track Log 和 Audit Log
  // const trackLog2: TrackLog = {
  //   kind: 'TRACK_LOG',
  //   type: 'CLICK_BUTTON',
  //   path: '/space',
  //   id: '123',
  //   class: 'button',
  //   // createdAt: hoursAgo23,
  // };
  // const auditLog2: AuditLog = {
  //   kind: 'ACCESS_LOG',
  //   action: {
  //     type: 'http',
  //     path: '/space',
  //   },
  //   headers: {},
  //   client: {
  //     hostname: '127.0.0.1',
  //     ip: '127.0.0.1',
  //     version: '0.0.1',
  //   },
  //   server: {
  //     hostname: '127.0.0.1',
  //     platform: 'MacOS',
  //   },
  // };

  // // 写入 Search Index
  // await client.write({
  //   userId: user.id,
  //   spaceId: space.id,
  //   indexName: 'TRACK_LOG',
  //   indexData: trackLog1,
  //   createdAt: hoursAgo47,
  // });
  // await client.write({
  //   userId: user.id,
  //   spaceId: space.id,
  //   indexName: 'ACCESS_LOG',
  //   indexData: auditLog1,
  //   createdAt: hoursAgo47,
  // });
  // await client.write({
  //   userId: user.id,
  //   spaceId: space.id,
  //   indexName: 'TRACK_LOG',
  //   indexData: trackLog2,
  //   createdAt: hoursAgo23,
  // });
  // await client.write({
  //   userId: user.id,
  //   spaceId: space.id,
  //   indexName: 'ACCESS_LOG',
  //   indexData: auditLog2,
  //   createdAt: hoursAgo23,
  // });

  // // 查询前 10 条 TRACK_LOG
  // test('should have 2 track logs', async () => {
  //   // 默认排序
  //   let searchResult = await client.search('TRACK_LOG', {
  //     filter: {
  //       userId: user.id,
  //       spaceId: space.id,
  //     },
  //     skip: 0,
  //     limit: 10,
  //   });
  //   expect(searchResult.length).toEqual(2);
  //   expect(searchResult[0].data.indexData.type).toEqual('SSE');
  //   expect(searchResult[1].data.indexData.type).toEqual('CLICK_BUTTON');

  //   // 按时间倒序
  //   searchResult = await client.search('TRACK_LOG', {
  //     filter: {
  //       userId: user.id,
  //       spaceId: space.id,
  //     },
  //     sort: {
  //       createdAt: 'desc',
  //     },
  //     skip: 0,
  //     limit: 10,
  //   });
  //   expect(searchResult.length).toEqual(2);
  //   expect(searchResult[0].data.indexData.type).toEqual('CLICK_BUTTON');
  //   expect(searchResult[1].data.indexData.type).toEqual('SSE');
  // });

  // // 分页查询 TRACK_LOG
  // test('should have 1 track logs (page 2)', async () => {
  //   const searchResult = await client.search('TRACK_LOG', {
  //     filter: {
  //       userId: user.id,
  //       spaceId: space.id,
  //     },
  //     skip: 1,
  //     limit: 10,
  //   });
  //   expect(searchResult.length).toEqual(1);
  //   expect(searchResult[0].data.indexData.type).toEqual('CLICK_BUTTON');
  // });

  // // 查询 date1 的 TRACK_LOG
  // test('should have 1 track logs', async () => {
  //   let searchResult = await client.search('TRACK_LOG', {
  //     filter: {
  //       userId: user.id,
  //       spaceId: space.id,
  //       'indexData.type': 'SSE',
  //       'indexData.sse.name': 'online-user',
  //       // createdAt: hoursAgo47,
  //     },
  //     skip: 0,
  //     limit: 10,
  //   });
  //   expect(searchResult.length).toEqual(1);
  //   expect(searchResult[0].data.indexData.type).toEqual('SSE');

  //   searchResult = await client.search('TRACK_LOG', {
  //     filter: {
  //       userId: user.id,
  //       spaceId: space.id,
  //       'indexData.type': 'CLICK_BUTTON',
  //       'indexData.class': 'button',
  //       // createdAt: hoursAgo23,
  //     },
  //     skip: 0,
  //     limit: 10,
  //   });
  //   expect(searchResult.length).toEqual(1);
  //   expect(searchResult[0].data.indexData.type).toEqual('CLICK_BUTTON');
  // });

  // // 使用不存在的查询条件
  // test('should have 0 track logs', async () => {
  //   const searchResult = await client.search('TRACK_LOG', {
  //     filter: {
  //       userId: user.id,
  //       spaceId: space.id,
  //       'indexData.sse.name': 'online-user',
  //       createdAt: hoursAgo23,
  //     },
  //     skip: 0,
  //     limit: 10,
  //   });
  //   expect(searchResult.length).toEqual(0);
  // });

  // // 最近 1 天的 TRACK_LOG
  // test('should have 1 track logs (last day)', async () => {
  //   const searchResult = await client.search('TRACK_LOG', {
  //     filter: {
  //       userId: user.id,
  //       spaceId: space.id,
  //       createdAt: {
  //         $gt: now.subtract(1, 'day').toISOString(),
  //       },
  //     },
  //     skip: 0,
  //     limit: 10,
  //   });
  //   expect(searchResult.length).toEqual(1);
  //   expect(searchResult[0].data.indexData.type).toEqual('CLICK_BUTTON');
  // });

  // // 最近 1 天的 ACCESS_LOG
  // test('should have 1 audit logs', async () => {
  //   const searchResult = await client.search('ACCESS_LOG', {
  //     filter: {
  //       userId: user.id,
  //       spaceId: space.id,
  //       createdAt: {
  //         $gt: now.subtract(1, 'day').toISOString(),
  //       },
  //     },
  //     skip: 0,
  //     limit: 10,
  //   });
  //   expect(searchResult.length).toEqual(1);
  //   expect(searchResult[0].data.indexData.action.type).toEqual('http');
  //   expect(searchResult[0].data.indexData.server.platform).toEqual('MacOS');
  // });
};
