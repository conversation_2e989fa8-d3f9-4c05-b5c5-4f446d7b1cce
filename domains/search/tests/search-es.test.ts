import { describe, test } from 'vitest';
import { db } from '@bika/server-orm';
import { testSearchIndex } from './search-testcases';

const esClient = db.search.esClient.rawClient!;

/**
 * 测试 Search Index ES
 * 仅在有 ElasticSearch 时执行
 */
describe('Test Search Index ES', async () => {
  const skip = !esClient;

  // 如果没有 ElasticSearch，则跳过测试
  if (skip) {
    test.skip('Skip test for no ElasticSearch');
    return;
  }

  // 创建 ACCESS_LOG 索引
  // const auditLogIndex = 'ACCESS_LOG'.toLowerCase();
  // const auditLogIndices = Object.keys(await esClient.indices.get({ index: toIndexName(auditLogIndex, 'wildcard') }));
  // if (auditLogIndices.length) {
  //   await esClient.indices.delete({ index: auditLogIndices });
  // }
  // await esClient.indices.create({
  //   index: toIndexName(auditLogIndex),
  //   body: {
  //     mappings: {
  //       properties: {
  //         userId: { type: 'keyword' },
  //         spaceId: { type: 'keyword' },
  //         indexName: { type: 'keyword' },
  //         indexData: {
  //           type: 'nested',
  //           properties: {
  //             server: {
  //               type: 'nested',
  //               properties: {
  //                 hostname: { type: 'keyword' },
  //                 platform: { type: 'keyword' },
  //               },
  //             },
  //           },
  //         },
  //         createdAt: { type: 'date' },
  //       },
  //     },
  //   },
  // });

  // // 创建 TRACK_LOG 索引
  // const trackLogIndex = 'TRACK_LOG'.toLowerCase();
  // const trackLogIndices = Object.keys(await esClient.indices.get({ index: toIndexName(trackLogIndex, 'wildcard') }));
  // if (trackLogIndices.length) {
  //   await esClient.indices.delete({ index: trackLogIndices });
  // }
  // await esClient.indices.create({
  //   index: toIndexName(trackLogIndex),
  //   body: {
  //     mappings: {
  //       properties: {
  //         userId: { type: 'keyword' },
  //         spaceId: { type: 'keyword' },
  //         indexName: { type: 'keyword' },
  //         indexData: {
  //           type: 'nested',
  //           properties: {
  //             class: { type: 'keyword' },
  //             type: { type: 'keyword' },
  //             sse: {
  //               type: 'nested',
  //               properties: {
  //                 name: { type: 'keyword' },
  //                 userId: { type: 'keyword' },
  //                 route: { type: 'keyword' },
  //                 onlineSessionId: { type: 'keyword' },
  //               },
  //             },
  //           },
  //         },
  //         createdAt: { type: 'date' },
  //       },
  //     },
  //   },
  // });

  // await testSearchIndex(db.search);
  await testSearchIndex();
});
