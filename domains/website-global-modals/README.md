# AI Artifact Global Store

This directory contains the Zustand-based global state management system for AI Artifacts, enabling cross-page shared state management throughout the application.

## Overview

The AI Artifact Global Store replaces the deprecated `useGlobalState` pattern with a modern Zustand-based solution that provides:

- **Type Safety**: Full TypeScript support with proper type inference
- **Performance**: Better performance than window-based global state
- **Cross-Page Persistence**: State persists across page navigation
- **Backward Compatibility**: Existing code can be migrated gradually
- **Developer Experience**: Better debugging and dev tools support

## Files

### Core Implementation

- **`ai-artifact-global-store.ts`** - Main store implementation with Zustand
- **`ai-artifact-modal.tsx`** - Updated modal component using the new store
- **`ai-artifact-global-store.test.ts`** - Comprehensive test suite
- **`ai-artifact-store-examples.md`** - Usage examples and migration guide

## API Reference

### Types

```typescript
interface AIArtifactState {
  message: Message;
  tool: ToolInvocation;
  skillsets: SkillsetSelectDTO[];
}
```

### Hooks

#### `useAIArtifactGlobalStore()`

Direct access to the Zustand store.

```typescript
const { data, setData, clearData } = useAIArtifactGlobalStore();
```

**Returns:**
- `data?: AIArtifactState` - Current artifact state
- `setData: (data?: AIArtifactState) => void` - Set artifact state
- `clearData: () => void` - Clear artifact state

#### `useAIArtifactState()` (Recommended)

Convenience hook with better naming and additional utilities.

```typescript
const { artifact, setArtifact, clearArtifact, hasArtifact } = useAIArtifactState();
```

**Returns:**
- `artifact?: AIArtifactState` - Current artifact state
- `setArtifact: (data?: AIArtifactState) => void` - Set artifact state
- `clearArtifact: () => void` - Clear artifact state
- `hasArtifact: boolean` - Whether artifact data exists

#### `useGlobalState<T>(key: string)` (Backward Compatible)

Maintains the same interface as the deprecated `useGlobalState` for gradual migration.

```typescript
const [globalArtifact, setGlobalArtifact] = useGlobalState<AIArtifactState>('AI_ARTIFACT');
```

**Parameters:**
- `key: string` - Should be 'AI_ARTIFACT' for consistency

**Returns:**
- `[state, setState]` - Tuple similar to the original useGlobalState

## Usage Patterns

### Setting Artifact Data

```typescript
import { useAIArtifactState } from './ai-artifact-global-store';

function ChatComponent() {
  const { setArtifact } = useAIArtifactState();
  
  const handleShowArtifact = (message, tool, skillsets) => {
    setArtifact({ message, tool, skillsets });
    // Optionally show modal
    showUIModal({ name: 'AI_ARTIFACT' });
  };
}
```

### Consuming Artifact Data

```typescript
import { useAIArtifactState } from './ai-artifact-global-store';

function ArtifactViewer() {
  const { artifact, hasArtifact, clearArtifact } = useAIArtifactState();
  
  if (!hasArtifact) {
    return <div>No artifact to display</div>;
  }
  
  return (
    <div>
      <h1>Tool: {artifact.tool.toolName}</h1>
      <button onClick={clearArtifact}>Close</button>
    </div>
  );
}
```

### Cross-Page Navigation

```typescript
// Page A - Set artifact and navigate
function PageA() {
  const { setArtifact } = useAIArtifactState();
  const router = useRouter();
  
  const openArtifact = (data) => {
    setArtifact(data);
    router.push('/artifact-viewer');
  };
}

// Page B - Consume artifact
function PageB() {
  const { artifact } = useAIArtifactState();
  // artifact data is automatically available
}
```

## Migration Guide

### From Deprecated useGlobalState

**Before:**
```typescript
import { useGlobalState } from '@bika/types/website/context';

const [globalArtifact] = useGlobalState<{
  message: Message;
  tool: ToolInvocation;
  skillsets: SkillsetSelectDTO[];
}>('AI_ARTIFACT');
```

**After (Recommended):**
```typescript
import { useAIArtifactState } from './ai-artifact-global-store';

const { artifact } = useAIArtifactState();
```

**After (Backward Compatible):**
```typescript
import { useGlobalState } from './ai-artifact-global-store';
import type { AIArtifactState } from './ai-artifact-global-store';

const [globalArtifact] = useGlobalState<AIArtifactState>('AI_ARTIFACT');
```

## Integration Points

The store is integrated with:

1. **AI Chat UI** (`domains/ai/client/chat/ai-chat-ui.tsx`)
   - Sets artifact state when tools are selected
   - Manages COPILOT mode artifact display

2. **Artifact Header** (`domains/ai/client/chat/artifacts/components/artifact-header.tsx`)
   - Expand button to show artifact in modal
   - Modal state management

3. **Global Modal System** (`domains/website/client/context/global/modal/global-modals-view.tsx`)
   - AI_ARTIFACT modal integration

## Testing

Run tests with:
```bash
npm test ai-artifact-global-store.test.ts
```

The test suite covers:
- Basic store operations
- Hook synchronization
- Backward compatibility
- Cross-hook state sharing

## Benefits

1. **Type Safety**: Full TypeScript support prevents runtime errors
2. **Performance**: Zustand is more efficient than window-based state
3. **Developer Experience**: Better debugging with Redux DevTools
4. **Maintainability**: Cleaner API and better separation of concerns
5. **Future-Proof**: Modern state management pattern
6. **Backward Compatibility**: Gradual migration path
