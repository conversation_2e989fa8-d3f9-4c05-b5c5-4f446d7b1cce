import { AIChatArtifact } from '@bika/domains/ai/client/chat/artifacts/ai-artifact';
import { useAIArtifactGlobalStore } from './ai-artifact-global-store';

interface Props {
  onClickClose: () => void;
}
export function AIArtifactModal(props: Props) {
  const { data: globalArtifact } = useAIArtifactGlobalStore();

  if (!globalArtifact) {
    return <>NO Artifact</>;
  }
  return (
    <AIChatArtifact
      skillsets={globalArtifact?.skillsets ?? []}
      message={globalArtifact?.message}
      tool={globalArtifact?.tool}
      onClickClose={props.onClickClose}
      isModal={true}
      isCopilot={true}
      sx={{
        height: '100%',
      }}
    />
  );
}
