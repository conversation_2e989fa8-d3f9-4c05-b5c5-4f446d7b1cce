/**
 * @vitest-environment jsdom
 */

import { renderHook, act } from '@testing-library/react';
import { vi } from 'vitest';
import type { Message, ToolInvocation } from '@ai-sdk/ui-utils';
import type { SkillsetSelectDTO } from '@bika/types/skill/dto';
import { 
  useAIArtifactGlobalStore, 
  useAIArtifactState, 
  useGlobalState,
  type AIArtifactState 
} from './ai-artifact-global-store';

// Mock data for testing
const mockMessage: Message = {
  id: 'test-message-id',
  role: 'assistant',
  content: 'Test message content',
};

const mockToolInvocation: ToolInvocation = {
  toolCallId: 'test-tool-call-id',
  toolName: 'test-tool',
  state: 'result',
  args: { test: 'args' },
  result: { test: 'result' },
};

const mockSkillsets: SkillsetSelectDTO[] = [
  { kind: 'preset', key: 'default' },
  { kind: 'preset', key: 'bika-database' },
];

const mockArtifactState: AIArtifactState = {
  message: mockMessage,
  tool: mockToolInvocation,
  skillsets: mockSkillsets,
};

describe('AI Artifact Global Store', () => {
  beforeEach(() => {
    // Clear the store before each test
    const { result } = renderHook(() => useAIArtifactGlobalStore());
    act(() => {
      result.current.clearData();
    });
  });

  describe('useAIArtifactGlobalStore', () => {
    it('should initialize with undefined data', () => {
      const { result } = renderHook(() => useAIArtifactGlobalStore());
      
      expect(result.current.data).toBeUndefined();
    });

    it('should set and get artifact data', () => {
      const { result } = renderHook(() => useAIArtifactGlobalStore());
      
      act(() => {
        result.current.setData(mockArtifactState);
      });
      
      expect(result.current.data).toEqual(mockArtifactState);
    });

    it('should clear artifact data', () => {
      const { result } = renderHook(() => useAIArtifactGlobalStore());
      
      // Set data first
      act(() => {
        result.current.setData(mockArtifactState);
      });
      
      expect(result.current.data).toEqual(mockArtifactState);
      
      // Clear data
      act(() => {
        result.current.clearData();
      });
      
      expect(result.current.data).toBeUndefined();
    });
  });

  describe('useAIArtifactState', () => {
    it('should provide convenience methods', () => {
      const { result } = renderHook(() => useAIArtifactState());
      
      expect(result.current.artifact).toBeUndefined();
      expect(result.current.hasArtifact).toBe(false);
      expect(typeof result.current.setArtifact).toBe('function');
      expect(typeof result.current.clearArtifact).toBe('function');
    });

    it('should set artifact using convenience method', () => {
      const { result } = renderHook(() => useAIArtifactState());
      
      act(() => {
        result.current.setArtifact(mockArtifactState);
      });
      
      expect(result.current.artifact).toEqual(mockArtifactState);
      expect(result.current.hasArtifact).toBe(true);
    });

    it('should clear artifact using convenience method', () => {
      const { result } = renderHook(() => useAIArtifactState());
      
      // Set data first
      act(() => {
        result.current.setArtifact(mockArtifactState);
      });
      
      expect(result.current.hasArtifact).toBe(true);
      
      // Clear data
      act(() => {
        result.current.clearArtifact();
      });
      
      expect(result.current.artifact).toBeUndefined();
      expect(result.current.hasArtifact).toBe(false);
    });
  });

  describe('useGlobalState (backward compatibility)', () => {
    it('should work with AI_ARTIFACT key', () => {
      const { result } = renderHook(() => useGlobalState<AIArtifactState>('AI_ARTIFACT'));
      
      const [artifact, setArtifact] = result.current;
      
      expect(artifact).toBeUndefined();
      
      act(() => {
        setArtifact(mockArtifactState);
      });
      
      expect(result.current[0]).toEqual(mockArtifactState);
    });

    it('should warn for incorrect keys', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

      renderHook(() => useGlobalState<AIArtifactState>('WRONG_KEY'));

      expect(consoleSpy).toHaveBeenCalledWith(
        'useGlobalState called with unexpected key: WRONG_KEY. Expected \'AI_ARTIFACT\''
      );

      consoleSpy.mockRestore();
    });
  });

  describe('Cross-hook synchronization', () => {
    it('should synchronize state between different hooks', () => {
      const { result: storeResult } = renderHook(() => useAIArtifactGlobalStore());
      const { result: stateResult } = renderHook(() => useAIArtifactState());
      const { result: globalResult } = renderHook(() => useGlobalState<AIArtifactState>('AI_ARTIFACT'));
      
      // Set data using store hook
      act(() => {
        storeResult.current.setData(mockArtifactState);
      });
      
      // All hooks should have the same data
      expect(storeResult.current.data).toEqual(mockArtifactState);
      expect(stateResult.current.artifact).toEqual(mockArtifactState);
      expect(globalResult.current[0]).toEqual(mockArtifactState);
      
      // Clear using state hook
      act(() => {
        stateResult.current.clearArtifact();
      });
      
      // All hooks should be cleared
      expect(storeResult.current.data).toBeUndefined();
      expect(stateResult.current.artifact).toBeUndefined();
      expect(globalResult.current[0]).toBeUndefined();
    });
  });
});
