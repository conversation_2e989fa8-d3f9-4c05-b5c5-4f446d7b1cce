'use client';

import type { Message, ToolInvocation } from '@ai-sdk/ui-utils';
import { create } from 'zustand';
import type { SkillsetSelectDTO } from '@bika/types/skill/dto';

/**
 * AI Artifact state interface for cross-page shared state management
 */
export interface AIArtifactState {
  message: Message;
  tool: ToolInvocation;
  skillsets: SkillsetSelectDTO[];
}

/**
 * AI Artifact store interface with actions
 */
interface AIArtifactStore {
  data?: AIArtifactState;
  setData: (data?: AIArtifactState) => void;
  clearData: () => void;
}

/**
 * Zustand store for AI Artifact global state management
 * 
 * This store enables cross-page shared state for AI Artifacts, allowing
 * components to share message, tool invocation, and skillsets data
 * across different pages and components.
 * 
 * Usage:
 * ```typescript
 * const { data, setData, clearData } = useAIArtifactGlobalStore();
 * 
 * // Set artifact data
 * setData({
 *   message: messageData,
 *   tool: toolInvocation,
 *   skillsets: selectedSkillsets
 * });
 * 
 * // Access artifact data
 * const artifactData = data;
 * 
 * // Clear artifact data
 * clearData();
 * ```
 */
export const useAIArtifactGlobalStore = create<AIArtifactStore>((set) => ({
  data: undefined,
  setData: (data) => set(() => ({ data })),
  clearData: () => set(() => ({ data: undefined })),
}));

/**
 * Hook that mimics the useGlobalState pattern for backward compatibility
 *
 * This hook provides the same interface as the deprecated useGlobalState
 * but uses the new Zustand store internally.
 *
 * @param key - The state key (should be 'AI_ARTIFACT' for consistency)
 * @returns [state, setState] tuple similar to useGlobalState
 */
export function useGlobalState<T extends AIArtifactState>(
  key: string
): [T | undefined, (value: T | undefined) => void] {
  const { data, setData } = useAIArtifactGlobalStore();

  // Ensure the key is correct for type safety
  if (key !== 'AI_ARTIFACT') {
    console.warn(`useGlobalState called with unexpected key: ${key}. Expected 'AI_ARTIFACT'`);
  }

  return [data as T | undefined, setData as (value: T | undefined) => void];
}

/**
 * Convenience hook for AI Artifact state management
 *
 * This hook provides a more specific interface for AI Artifact state
 * with better type safety and clearer intent.
 *
 * @returns Object with artifact data and management functions
 */
export function useAIArtifactState() {
  const { data, setData, clearData } = useAIArtifactGlobalStore();

  return {
    /** Current AI Artifact state */
    artifact: data,
    /** Set the AI Artifact state */
    setArtifact: setData,
    /** Clear the AI Artifact state */
    clearArtifact: clearData,
    /** Check if artifact data exists */
    hasArtifact: !!data,
  };
}
