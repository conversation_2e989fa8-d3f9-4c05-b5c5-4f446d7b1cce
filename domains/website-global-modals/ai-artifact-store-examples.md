# AI Artifact Global Store Usage Examples

This document provides comprehensive examples of how to use the new Zustand-based AI Artifact global store for cross-page shared state management.

## Basic Usage

### 1. Using the Direct Store Hook

```typescript
import { useAIArtifactGlobalStore } from './ai-artifact-global-store';

function MyComponent() {
  const { data, setData, clearData } = useAIArtifactGlobalStore();
  
  // Set artifact data
  const handleSetArtifact = () => {
    setData({
      message: messageData,
      tool: toolInvocation,
      skillsets: selectedSkillsets
    });
  };
  
  // Access artifact data
  const artifactData = data;
  
  // Clear artifact data
  const handleClear = () => {
    clearData();
  };
  
  return (
    <div>
      {data ? (
        <div>Artifact loaded: {data.tool.toolName}</div>
      ) : (
        <div>No artifact</div>
      )}
    </div>
  );
}
```

### 2. Using the Convenience Hook

```typescript
import { useAIArtifactState } from './ai-artifact-global-store';

function MyComponent() {
  const { artifact, setArtifact, clearArtifact, hasArtifact } = useAIArtifactState();
  
  const handleSetArtifact = () => {
    setArtifact({
      message: messageData,
      tool: toolInvocation,
      skillsets: selectedSkillsets
    });
  };
  
  return (
    <div>
      {hasArtifact ? (
        <div>Artifact: {artifact?.tool.toolName}</div>
      ) : (
        <div>No artifact available</div>
      )}
      <button onClick={handleSetArtifact}>Set Artifact</button>
      <button onClick={clearArtifact}>Clear Artifact</button>
    </div>
  );
}
```

### 3. Backward Compatible Usage (Legacy Pattern)

```typescript
import { useGlobalState } from './ai-artifact-global-store';
import type { AIArtifactState } from './ai-artifact-global-store';

function LegacyComponent() {
  // This maintains the same interface as the deprecated useGlobalState
  const [globalArtifact, setGlobalArtifact] = useGlobalState<AIArtifactState>('AI_ARTIFACT');
  
  const handleUpdate = () => {
    setGlobalArtifact({
      message: messageData,
      tool: toolInvocation,
      skillsets: selectedSkillsets
    });
  };
  
  return (
    <div>
      {globalArtifact ? (
        <div>Legacy artifact: {globalArtifact.tool.toolName}</div>
      ) : (
        <div>No artifact</div>
      )}
    </div>
  );
}
```

## Advanced Usage Patterns

### 1. Cross-Page Navigation with State Persistence

```typescript
// Page A - Setting the artifact
import { useAIArtifactState } from './ai-artifact-global-store';
import { useRouter } from 'next/router';

function PageA() {
  const { setArtifact } = useAIArtifactState();
  const router = useRouter();
  
  const handleOpenArtifact = (message, tool, skillsets) => {
    // Set the artifact state
    setArtifact({ message, tool, skillsets });
    
    // Navigate to the artifact page
    router.push('/artifact-viewer');
  };
  
  return <button onClick={handleOpenArtifact}>Open Artifact</button>;
}

// Page B - Consuming the artifact
function PageB() {
  const { artifact, hasArtifact } = useAIArtifactState();
  
  if (!hasArtifact) {
    return <div>No artifact to display</div>;
  }
  
  return (
    <div>
      <h1>Artifact Viewer</h1>
      <div>Tool: {artifact.tool.toolName}</div>
      <div>Message: {artifact.message.content}</div>
      <div>Skillsets: {artifact.skillsets.length}</div>
    </div>
  );
}
```

### 2. Modal Integration

```typescript
import { useAIArtifactState } from './ai-artifact-global-store';
import { useGlobalContext } from '@bika/types/website/context';

function ChatComponent() {
  const { setArtifact } = useAIArtifactState();
  const { showUIModal } = useGlobalContext();
  
  const handleShowArtifact = (message, tool, skillsets) => {
    // Set the artifact state
    setArtifact({ message, tool, skillsets });
    
    // Open the artifact modal
    showUIModal({
      name: 'AI_ARTIFACT'
    });
  };
  
  return <button onClick={handleShowArtifact}>Show Artifact Modal</button>;
}
```

### 3. Type-Safe Usage with Custom Types

```typescript
import type { Message, ToolInvocation } from '@ai-sdk/ui-utils';
import type { SkillsetSelectDTO } from '@bika/types/skill/dto';
import { useAIArtifactState } from './ai-artifact-global-store';

interface CustomArtifactData {
  message: Message;
  tool: ToolInvocation;
  skillsets: SkillsetSelectDTO[];
  customField?: string;
}

function TypeSafeComponent() {
  const { artifact, setArtifact } = useAIArtifactState();
  
  // Type-safe access to artifact properties
  const toolName = artifact?.tool.toolName;
  const messageContent = artifact?.message.content;
  const skillsetCount = artifact?.skillsets.length ?? 0;
  
  const handleCustomUpdate = () => {
    if (artifact) {
      // Extend the existing artifact with custom data
      setArtifact({
        ...artifact,
        // Add any additional custom fields as needed
      });
    }
  };
  
  return (
    <div>
      <div>Tool: {toolName}</div>
      <div>Message: {messageContent}</div>
      <div>Skillsets: {skillsetCount}</div>
    </div>
  );
}
```

## Migration Guide

### From Deprecated useGlobalState

**Before:**
```typescript
import { useGlobalState } from '@bika/types/website/context';

const [globalArtifact] = useGlobalState<{ message: Message; tool: ToolInvocation; skillsets: SkillsetSelectDTO[] }>('AI_ARTIFACT');
```

**After (Recommended):**
```typescript
import { useAIArtifactState } from './ai-artifact-global-store';

const { artifact } = useAIArtifactState();
```

**After (Backward Compatible):**
```typescript
import { useGlobalState } from './ai-artifact-global-store';
import type { AIArtifactState } from './ai-artifact-global-store';

const [globalArtifact] = useGlobalState<AIArtifactState>('AI_ARTIFACT');
```

## Benefits

1. **Type Safety**: Full TypeScript support with proper type inference
2. **Performance**: Zustand provides better performance than the window-based global state
3. **Developer Experience**: Better debugging and dev tools support
4. **Cross-Page Persistence**: State persists across page navigation within the application
5. **Backward Compatibility**: Existing code can be migrated gradually
6. **Cleaner API**: More intuitive hooks with better naming and functionality
