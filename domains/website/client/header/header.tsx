'use client';

import React, { useEffect, useState } from 'react';
import NavLink from '@bika/ui/components/nav-link/index';
import { Typography } from '@bika/ui/website/typography/index';
import style from './index.module.css';

interface Props {
  renderUserButton: React.ReactNode;
  logo: React.ReactNode;
  menu: {
    href: string;
    label: string;
    exact?: boolean;
    target?: string;
  }[];
  extraButton?: React.ReactNode;
}

export function DesktopHeader(props: Props) {
  const [isFixed, setIsFixed] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsFixed(window.scrollY >= 60);
    };
    window.addEventListener('scroll', handleScroll);
    handleScroll();
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const fixedStyle: React.CSSProperties = isFixed
    ? {
        position: 'fixed',
        top: 0,
        // left: 0,
        // right: 0,
        width: '100%',
        zIndex: 100,
        transition: 'all 0.2s',
      }
    : {};

  return (
    <header className={style.header}>
      <div className={style.headerInner} style={fixedStyle}>
        <div className={style.headerInnerMain}>
          <div className={style.logo}>{props.logo}</div>

          <div className={style.center}>
            <div className={style.menu}>
              {props.menu.map((item, index) => (
                <NavLink
                  exact={item.exact}
                  key={index}
                  className={style.link}
                  activeClassName={style.linkActive}
                  target={item.target}
                  href={item.href}
                >
                  <Typography level={6}>{item.label}</Typography>
                </NavLink>
              ))}
            </div>
          </div>
          <div className={style.right}>
            {props.extraButton}
            {props.renderUserButton}
          </div>
        </div>
      </div>
    </header>
  );
}
