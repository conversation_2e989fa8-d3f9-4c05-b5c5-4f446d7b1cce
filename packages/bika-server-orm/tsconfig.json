{
  "extends": "../../scripts/tsconfig/lib.tsconfig.json",
  "compilerOptions": {
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "module": "ESNext",
    "moduleResolution": "Bundler",
    "jsx": "preserve",
    "types": [
      "@types/bun",
      "@types/node"
    ],
    "paths": {
      //   //   "@bika/i18n": ["../../content/i18n"],
      //   "@bika/templates": ["../../content/templates1/src"],
      // "@bika/templates/*": ["../../content/templates/*"]
    }
    // },
    // "ts-node": {
    //   "compilerOptions": {
    //     "moduleResolution": "NodeNext"
    //     // compilerOptions specified here will override those declared below,
    //     // but *only* in ts-node.  Useful if you want ts-node and tsc to use
    //     // different options with a single tsconfig.json.
    //   },
    //   // "esm": true
  },
  "include": ["./**.ts", "**/*.ts"],
  "exclude": [
    "./node_modules"
  ]
}