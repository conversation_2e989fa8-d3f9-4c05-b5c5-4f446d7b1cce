import type { MetadataRoute } from 'next';

/**
 * MetadataRoute.Robots这个结构体，转成 robots.txt 格式规范
 *
 * @param robots
 */
export function robotsTxtConvert(robots: MetadataRoute.Robots) {
  const lines: string[] = [];

  const rules = Array.isArray(robots.rules) ? robots.rules : [robots.rules];

  rules.forEach((rule, index) => {
    if (rule.userAgent) {
      const userAgents = Array.isArray(rule.userAgent) ? rule.userAgent : [rule.userAgent];
      userAgents.forEach((ua) => lines.push(`User-agent: ${ua}`));
    }

    if (rule.allow) {
      const allows = Array.isArray(rule.allow) ? rule.allow : [rule.allow];
      allows.forEach((path) => lines.push(`Allow: ${path}`));
    }

    if (rule.disallow) {
      const disallows = Array.isArray(rule.disallow) ? rule.disallow : [rule.disallow];
      disallows.forEach((path) => lines.push(`Disallow: ${path}`));
    }

    if (rule.crawlDelay !== undefined) {
      lines.push(`Crawl-delay: ${rule.crawlDelay}`);
    }

    // 只在非最后一个规则后添加空行
    if (index < rules.length - 1) {
      lines.push('');
    }
  });

  // Sitemap 应该在所有规则之后，用空行分隔
  if (robots.sitemap) {
    if (lines.length > 0) {
      lines.push('');
    }
    const sitemaps = Array.isArray(robots.sitemap) ? robots.sitemap : [robots.sitemap];
    sitemaps.forEach((sitemap) => lines.push(`Sitemap: ${sitemap}`));
  }

  // Host 指令（如果需要）
  if (robots.host) {
    if (lines.length > 0) {
      lines.push('');
    }
    lines.push(`Host: ${robots.host}`);
  }

  return lines.join('\n');
}
