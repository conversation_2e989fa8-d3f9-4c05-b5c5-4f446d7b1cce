import type { Metadata } from 'next';

/**
 *
 * Auto postprocess fill in the metadata's openGraph
 *
 * @param title
 */
export function postprocessPageMetadata(metadata: Metadata): Metadata {
  const newMetadata: Metadata = {
    ...metadata,
  };

  if (!metadata.openGraph) {
    const title =
      typeof metadata.title === 'string' ? metadata.title : (metadata.title as { default?: string })?.default || '';

    const description = typeof metadata.description === 'string' ? metadata.description : '';

    const locale = (metadata as any).locale || 'en-US';

    newMetadata.openGraph = {
      title,
      description,
      type: 'website',
      locale,
    };
  }

  return newMetadata;
}
