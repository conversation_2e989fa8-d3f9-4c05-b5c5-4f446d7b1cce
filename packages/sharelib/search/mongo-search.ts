// import { FilterQuery } from 'mongoose';
// import { DateTimeSO } from '@bika/types/system';
// import type { SearchIndexMapping, SearchIndexData, SearchIndexQuery, ISearchClient } from './types';
// import { MongoClient, SearchIndexModel } from '../mongo';

// /**
//  * MongoDB Search Client
//  */
// export class MongoSearchClient implements ISearchClient {
//   private mongoClient: typeof MongoClient;

//   constructor(mongoClient: typeof MongoClient) {
//     this.mongoClient = mongoClient;
//   }

//   /**
//    * 写入 mongodb
//    * @param data search index data
//    */
//   public async write<T extends keyof SearchIndexMapping>(data: SearchIndexData<T>) {
//     const model: SearchIndexModel = {
//       userId: data.userId,
//       spaceId: data.spaceId,
//       indexName: data.indexName,
//       indexData: data.indexData,
//       createdBy: data.userId,
//       updatedBy: data.userId,
//       createdAt: DateTimeSO.fromISOString(data.createdAt).toDate(),
//       updatedAt: DateTimeSO.fromISOString(data.createdAt).toDate(),
//     };

//     await this.mongoClient.searchIndex.create(model);
//   }

//   /**
//    * 从 mongodb 搜索
//    * @param name search index name
//    * @param query search conditions
//    */
//   public async search<T extends keyof SearchIndexMapping>(
//     name: T,
//     query: SearchIndexQuery<T>,
//   ): Promise<Array<SearchIndexData<T>>> {
//     // 构造查询条件
//     const filter: FilterQuery<SearchIndexModel> = {
//       ...query.filter,
//       // 强制指定 indexName，仅允许查询同一种类型
//       indexName: name,
//     };

//     // 从 MongoDB 查询
//     let mongoQuerier = this.mongoClient.searchIndex.find(filter);
//     if (query.sort) {
//       mongoQuerier = mongoQuerier.sort(query.sort);
//     }
//     if (query.skip) {
//       mongoQuerier = mongoQuerier.skip(query.skip);
//     }
//     if (query.limit) {
//       mongoQuerier = mongoQuerier.limit(query.limit);
//     }

//     const pos: SearchIndexModel[] = await mongoQuerier;
//     return pos.map((po) => ({
//       userId: po.userId!,
//       spaceId: po.spaceId!,
//       indexName: po.indexName as T,
//       indexData: po.indexData as SearchIndexMapping[T],
//       createdAt: po.createdAt.toISOString(),
//     }));
//   }
// }
