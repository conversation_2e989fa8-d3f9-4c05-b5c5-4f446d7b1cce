import assert from 'assert';
import { Client, TransportRequestOptionsWithOutMeta, estypes } from '@elastic/elasticsearch';
// import * as TB from '@elastic/elasticsearch/lib/api/typesWithBodyKey';
import { ConnectionString } from 'connection-string';
import dayjs from 'dayjs';
import { SearchIndexSuffixMode } from './types';

type TBSearchRequest = estypes.SearchRequest;

/**
 * Elastic Search Client
 *
 * Elastic Search客户端，写由运维侧通过消费者(/api/cron/olap)、ETL工具（Flink CDC 、LogStash、Debezium）等把数据搬过去
 *
 */
export class ESClient {
  private _client?: Client;

  private _indexPrefix?: string;

  constructor(indexPrefix?: string) {
    if (!process.env.ELASTIC_SEARCH_URL) {
      return;
    }

    const cs = new ConnectionString(process.env.ELASTIC_SEARCH_URL);
    let nodeHost = `${cs.protocol}://${cs.hostname}`;
    if (cs.port) {
      nodeHost += `:${cs.port}`;
    }

    // const appEnv = getAppEnv();
    // let prefix = `bika-${appEnv}`.toLowerCase(); // 默认prefix是appenv

    let prefix: string | undefined;
    if (indexPrefix && indexPrefix.length > 0) {
      // 如果有配置indexPrefix，取它作为prefix
      prefix = indexPrefix.toLowerCase().replace(/_/g, '-');
    } else if (cs.path && cs.path.length > 0) {
      // 如果有配置path，取第一个path作为prefix
      prefix = cs.path[0].toLowerCase().replace(/_/g, '-');
    }
    this._indexPrefix = prefix;

    // 如果有auth，使用 BasicAuth
    if (cs?.params?.auth) {
      this._client = new Client({
        node: nodeHost, // Elasticsearch endpoint
        auth: {
          username: cs.user!,
          password: cs.password!,
        },
      });
      return;
    }

    // 如果没有auth，默认使用 ApiKeyAuth
    this._client = new Client({
      node: nodeHost, // Elasticsearch endpoint
      auth: {
        apiKey: {
          // API key ID and secret
          id: cs.user!,
          api_key: cs.password!,
        },
      },
    });
  }

  /**
   * 获取原始的ES客户端，暂时仅在 SearchIndex 中使用，后续可能移除
   */
  get rawClient() {
    return this._client;
  }

  async ping() {
    if (!this._client) {
      return null;
    }
    return this._client.ping();
  }

  /**
   * 解析索引名称
   *
   * today 放入今天日期，通常用于写入索引；
   * wildcard 放入 *，通常用于查询索引；
   * exact，不带 -* 和 -YYYY-MM-DD，具体的，通常用于查询具体的索引，带上 -index（之所以带上一个后戳，方便以后随时改成 -*** 也兼容)
   *
   * @param index
   * @param mode
   * @returns
   */
  parseIndexName(index: string, mode: SearchIndexSuffixMode = 'exact') {
    const fetchSuffix = (): string => {
      switch (mode) {
        case 'today':
          return dayjs().format('YYYY-MM-DD');
        case 'wildcard':
          return '*';
        case 'exact':
          return 'index';
        default:
          assert(typeof mode === 'object');
          return mode.custom;
      }
    };
    const suffix = fetchSuffix();

    return `${this._indexPrefix}-${index}-${suffix}`.replace(/_/g, '-').toLowerCase();
  }

  /**
   * ES 搜索
   */
  async search<TDocument = unknown, TAggregations = Record<string, estypes.AggregationsAggregate>>(
    params: TBSearchRequest,
    options?: TransportRequestOptionsWithOutMeta,
    mode: SearchIndexSuffixMode = 'wildcard',
  ): Promise<estypes.SearchResponse<TDocument, TAggregations>> {
    assert(this._client);
    const req = { ...params };

    // 转换 indexName
    if (req.index) {
      const indices = Array.isArray(req.index) ? req.index : [req.index];
      req.index = indices.map((index) => this.parseIndexName(index, mode));
    }

    // 从 ES 搜索
    return this._client.search(req, options);
  }
}
