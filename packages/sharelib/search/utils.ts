import assert from 'node:assert';
import { estypes } from '@elastic/elasticsearch';
import type { SearchIndexRequest } from './types';

type QueryDslQueryContainer = estypes.QueryDslQueryContainer;

/**
 * Mongo 查询条件转换为 ES 查询条件
 */
class Mongo2ESQueryConvertor {
  /**
   * 将 Mongo 查询条件转换为等价的 ES 查询条件
   *
   * @param query Mongo 查询条件
   * @returns ES 查询条件
   */
  static convert<T>(query: SearchIndexRequest<T>['filter']): QueryDslQueryContainer {
    if (!query) {
      return { bool: { must: [], should: [] } };
    }
    // Empty 条件
    const keys = Object.keys(query);
    if (keys.length === 0) {
      return { bool: { must: [], should: [] } };
    }

    // And 条件
    if (query.$and?.length) {
      return Mongo2ESQueryConvertor.convertAndCondition(query.$and);
    }

    // Or 条件
    if (query.$or?.length) {
      return Mongo2ESQueryConvertor.convertOrCondition(query.$or);
    }

    // 检查是否有不支持的逻辑操作符
    assert(
      Object.keys(query).every((key) => !key.startsWith('$')),
      'Unsupported logical operator',
    );

    // 单个条件，返回单个 condition
    if (keys.length === 1) {
      const key = keys[0];
      const val = query[key];
      return Mongo2ESQueryConvertor.convertSingleCondition(key, val);
    }

    // 多个条件，返回带 bool 的 condition
    const conditions = Object.entries(query).map(([key, val]) =>
      Mongo2ESQueryConvertor.convertSingleCondition(key, val),
    );
    return {
      bool: {
        must: conditions,
        should: [],
      },
    };
  }

  /**
   * 转换 And 条件
   */
  private static convertAndCondition<T>(
    andCondition: NonNullable<NonNullable<SearchIndexRequest<T>['filter']>['$and']>,
  ): QueryDslQueryContainer {
    const must = andCondition.map(this.convert);
    return {
      bool: {
        must,
        should: [],
      },
    };
  }

  /**
   * 转换 Or 条件
   */
  private static convertOrCondition<T>(
    orCondition: NonNullable<NonNullable<SearchIndexRequest<T>['filter']>['$or']>,
  ): QueryDslQueryContainer {
    const should = orCondition.map(this.convert);
    return {
      bool: {
        should,
        minimum_should_match: 1,
      },
    };
  }

  /**
   * 转换单个条件
   */
  private static readonly convertSingleCondition = (key: string, val: unknown): QueryDslQueryContainer => {
    let condition: QueryDslQueryContainer;

    // 区间条件
    if (typeof val === 'object' && val !== null && !Array.isArray(val)) {
      const range = val as Record<string, number>;

      const rangeMap = Object.entries(range).reduce((acc, [op, opValue]) => {
        switch (op) {
          case '$gt':
            return { ...acc, gt: opValue };
          case '$lt':
            return { ...acc, lt: opValue };
          case '$gte':
            return { ...acc, gte: opValue };
          case '$lte':
            return { ...acc, lte: opValue };
          default:
            throw new Error(`Unsupported comparison operator: ${op}`);
        }
      }, {});

      condition = { range: { [key]: rangeMap } };
    }
    // 相等条件
    else {
      // 由于我们不手工建mapping，所有字段都是 text 类型，无法精准匹配，要使用 keyword
      condition = { term: { [`${key}.keyword`]: val } };
    }

    // // nested key
    // const pos = key.lastIndexOf('.');
    // if (pos !== -1) {
    //   const path = key.slice(0, pos);
    //   return {
    //     nested: {
    //       path,
    //       query: condition,
    //     },
    //   };
    // }

    return condition;
  };
}

export { Mongo2ESQueryConvertor as _Mongo2ESQueryConvertor };
