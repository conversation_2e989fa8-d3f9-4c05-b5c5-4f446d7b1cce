import type { FilterQuery } from 'mongoose';

// 默认'exact'，+= '-index'
export type SearchIndexSuffixMode = 'today' | 'wildcard' | 'exact' | { custom: string };

/**
 * Search Index write函数参数
 */
export interface SearchIndexProps<T> {
  // Document ID
  id?: string;
  indexSuffixMode?: SearchIndexSuffixMode;
  indexData: T;

  userId?: string;
  spaceId?: string;
  // 一般不建议填这个，会自动生成，这里目前仅仅用于跑测试
  createdAt?: string;
}

/**
 * Search Index write到es的真正的数据结构，这是PO
 */
export interface SearchIndexDocumentModel<T> {
  // 版本，用于判定是否要重新写入
  version: string;
  userId?: string;
  spaceId?: string;
  indexData: T;
  createdAt: string;
}

export type SortOrder = 'asc' | 'desc';

/**
 * Search Index 查询条件
 */
export interface SearchIndexRequest<T> {
  // 任意字符串搜索
  q?: string;
  //  附加过滤条件
  filter?: FilterQuery<T>;
  sort?: Record<string, SortOrder>;
  skip?: number;
  limit?: number;
}

/**
 * 搜索结果返回
 */
export interface SearchIndexResponse<T> {
  id: string;
  score: number | null | undefined;
  data: SearchIndexDocumentModel<T>;
}

// export interface ISearcher {
//   write<T extends keyof SearchIndexDataMapping>(data: SearchIndexProps<T>): Promise<void>;
//   search<T extends keyof SearchIndexDataMapping>(
//     name: T,
//     query: SearchIndexRequest<T>,
//   ): Promise<Array<SearchIndexResponse<T>>>;
// }
