{"editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "editor.formatOnSave": true, "cSpell.diagnosticLevel": "Hint", "cSpell.words": ["ACTIVEPIECES", "AISDK", "AISO", "Antd", "apitable", "appspot", "automations", "basenext", "bika", "<PERSON>r", "<PERSON><PERSON><PERSON>", "datasheets", "Datasource", "DATERANGE", "datetime", "DEEPSEEK", "<PERSON><PERSON><PERSON>", "estypes", "etags", "exceljs", "<PERSON><PERSON><PERSON>", "firebaseapp", "formapp", "langchain", "mailparser", "minio", "Mobilephone", "msgtype", "nextjs", "NOSELECT", "olap", "openai", "Pkce", "reddot", "reddots", "SDKAI", "SDKAISO", "seqno", "sharelib", "<PERSON><PERSON>", "Skillset", "skillsets", "TAVILY", "tencentcloud", "todos", "TOOLAPP", "toolkits", "toolsdk", "Toolsets", "trpc", "uids", "updator", "vercel", "vika", "vikadata", "Voov", "webpush", "webset", "Websets", "WECHAT", "Wecom"], "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "files.watcherExclude": {"**/.git/objects/**": true, "**/node_modules/**": true}, "python.analysis.typeCheckingMode": "basic"}